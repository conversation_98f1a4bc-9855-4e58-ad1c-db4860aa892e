import os
import hashlib
import hmac
import requests
import logging
import json
import time
import uuid
import subprocess
import jwt
from datetime import datetime, timedelta
from webdav3.client import Client
from PIL import Image
from config import (
    WEBDAV_SECRET_KEY, WEBDAV_JWT_SECRET, DOWNLOAD_SERVER_URL, BACKEND_URL,
    SUPPORTED_FORMATS, DEFAULT_AUDIO_QUALITY, WEBDAV_TIMEOUT,
    MAX_RETRY_ATTEMPTS, HTTP_PROXY
)

logger = logging.getLogger(__name__)

def get_webdav_proxy_config():
    """
    获取WebDAV代理配置
    """
    if HTTP_PROXY:
        return {
            'http': HTTP_PROXY,
            'https': HTTP_PROXY
        }
    return None

def verify_webdav_config_signature(config_data, signature):
    """
    验证webdav配置签名
    """
    config_string = f"{config_data['url']}|{config_data['username']}|{config_data['password']}|{config_data['config_id']}"

    expected_signature = hmac.new(
        WEBDAV_SECRET_KEY.encode('utf-8'),
        config_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

    return hmac.compare_digest(expected_signature, signature)

def verify_webdav_jwt(token):
    """
    验证WebDAV JWT令牌
    """
    try:
        payload = jwt.decode(token, WEBDAV_JWT_SECRET, algorithms=['HS256'])
        return True, payload
    except Exception:
        return False, None

def test_webdav_connection(url, username, password):
    """
    测试WebDAV连接
    """
    try:
        proxies = get_webdav_proxy_config()
        response = requests.request(
            method="PROPFIND",
            url=url,
            auth=(username, password),
            timeout=10,
            headers={"Depth": "1"},
            proxies=proxies
        )
        
        if response.status_code in (200, 207):
            return True, ""
        elif response.status_code == 401:
            return False, "Authentication failed. Check username and password."
        elif response.status_code == 403:
            return False, "Access forbidden. Check your permissions."
        else:
            return False, f"Unexpected HTTP status code: {response.status_code}."
            
    except requests.exceptions.Timeout:
        return False, "The request timed out. The server may be unreachable or slow."
    except requests.exceptions.ConnectionError:
        return False, "Failed to connect to the WebDAV server. Check the URL and your network connection."
    except requests.exceptions.RequestException as e:
        return False, f"An error occurred while testing the WebDAV connection: {str(e)}"
    except Exception as e:
        return False, f"An unexpected error occurred: {str(e)}"

def generate_secure_hash_salt(partition, music_hash, format_type):
    """
    生成安全的hash_salt，用于从下载服务器获取文件
    """
    # 这里需要使用与后端相同的密钥
    DOWNLOADER_SECRET = '1p5kh&FPTA9W2VLF=n6~&J]u1cx'
    combined_string = f"{partition}_{music_hash}_{format_type}_{DOWNLOADER_SECRET}"
    return hashlib.md5(combined_string.encode('utf-8')).hexdigest()

def poll_download_status(title, album, artist, video_id, song_hash, request_format='webm', max_retries=20, retry_delay=2):
    """
    轮询后端获取下载链接，参考downloader.py接口
    Args:
        title: 歌曲标题
        album: 专辑名
        artist: 艺术家
        video_id: 视频ID（由用户传入）
        song_hash: 验证哈希（由用户传入）
        request_format: 请求格式，默认webm
        max_retries: 最大重试次数
        retry_delay: 重试间隔（秒）
    """
    # 构造请求数据，与downloader.py接口保持一致
    song_data = {
        'title': title,
        'album': album,
        'artist': artist,
        'videoId': video_id,
        'song_hash': song_hash,
        'request_format': request_format
    }

    for retry in range(max_retries):
        try:
            # 向后端请求下载状态
            response = requests.post(
                f"{BACKEND_URL}download/",
                json=song_data,
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                download_url = data.get('download_url')

                if download_url:
                    # 检查下载链接是否可用
                    if check_download_link(download_url):
                        logger.info(f"Download URL ready after {retry + 1} attempts")
                        return download_url

            elif response.status_code == 202:
                # 下载任务已创建，继续等待
                logger.info(f"Download task created, waiting... attempt {retry + 1}/{max_retries}")
            else:
                logger.warning(f"Download request failed with status {response.status_code}")

        except Exception as e:
            logger.warning(f"Download status check attempt {retry + 1} failed: {str(e)}")

        # 等待后重试
        if retry < max_retries - 1:
            time.sleep(retry_delay)

    raise Exception(f"Download URL not available after {max_retries} attempts")

def check_download_link(url):
    """
    检查下载链接是否可用
    """
    try:
        response = requests.head(url, timeout=10)
        return response.status_code == 200
    except:
        return False

def download_original_file(song_title, song_artist, album, video_id, song_hash, temp_dir):
    """
    参考前端逻辑下载原始webm文件
    Args:
        song_title: 歌曲标题
        song_artist: 艺术家
        album: 专辑名
        video_id: 视频ID（由用户传入）
        song_hash: 验证哈希（由用户传入）
        temp_dir: 临时目录
    """
    try:
        logger.info(f"Polling download status for {song_title} by {song_artist}")

        # 轮询获取下载链接
        download_url = poll_download_status(
            title=song_title,
            album=album,
            artist=song_artist,
            video_id=video_id,
            song_hash=song_hash,
            request_format='webm'
        )

        logger.info(f"Got download URL, downloading file...")

        # 下载文件
        response = requests.get(download_url, timeout=60, stream=True)
        response.raise_for_status()

        # 生成文件hash用于文件名（MD5）
        file_hash = hashlib.md5((song_title + album + song_artist).encode('utf-8')).hexdigest()

        # 保存到临时文件
        file_path = os.path.join(temp_dir, f"{file_hash}.webm")

        with open(file_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)

        logger.info(f"Downloaded original file: {file_path}")
        return file_path

    except Exception as e:
        logger.error(f"Failed to download original file for {song_hash}: {str(e)}")
        return None

def generate_song_hash(title, album, artist):
    """
    生成歌曲hash，与后端逻辑保持一致
    """
    combined = title + album + artist
    return hashlib.md5(combined.encode('utf-8')).hexdigest()

def download_cover_from_url(cover_url, temp_dir):
    """
    从URL直接下载封面图片
    Args:
        cover_url: 封面图片的完整URL
        temp_dir: 临时目录
    """
    try:
        logger.info(f"Downloading cover image from: {cover_url}")

        # 下载封面
        response = requests.get(cover_url, timeout=30)
        response.raise_for_status()

        # 检查内容类型
        content_type = response.headers.get('content-type', '').lower()

        # 根据URL或内容类型确定文件扩展名
        if 'webp' in content_type or cover_url.lower().endswith('.webp'):
            file_extension = 'webp'
        elif 'jpeg' in content_type or 'jpg' in content_type or cover_url.lower().endswith(('.jpg', '.jpeg')):
            file_extension = 'jpg'
        elif 'png' in content_type or cover_url.lower().endswith('.png'):
            file_extension = 'png'
        else:
            # 默认保存为webp
            file_extension = 'webp'

        # 生成文件名
        filename = f"cover_{uuid.uuid4().hex[:8]}.{file_extension}"
        original_path = os.path.join(temp_dir, filename)

        with open(original_path, 'wb') as f:
            f.write(response.content)

        logger.info(f"Downloaded cover image: {original_path}")

        # 如果是webp格式，需要裁剪为正方形并转为jpg
        if file_extension == 'webp':
            jpg_path = process_cover_image(original_path, temp_dir)
            return jpg_path
        else:
            # 对于其他格式，也进行处理以确保是正方形
            processed_path = process_cover_image(original_path, temp_dir)
            return processed_path

    except Exception as e:
        logger.warning(f"Failed to download cover image from {cover_url}: {str(e)}")
        return None

def process_cover_image(image_path, temp_dir, size=500, quality=90):
    """
    处理封面图片：裁剪为正方形并转为JPG格式
    参考前端imageProcessor逻辑
    """
    try:
        # 打开图片
        with Image.open(image_path) as img:
            # 转换为RGB模式（处理透明度）
            if img.mode in ('RGBA', 'LA', 'P'):
                # 创建白色背景
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                img = background
            elif img.mode != 'RGB':
                img = img.convert('RGB')

            # 计算正方形裁剪尺寸
            crop_size = min(img.width, img.height)
            offset_x = (img.width - crop_size) // 2
            offset_y = (img.height - crop_size) // 2

            # 裁剪为正方形
            img_cropped = img.crop((
                offset_x,
                offset_y,
                offset_x + crop_size,
                offset_y + crop_size
            ))

            # 调整大小
            img_resized = img_cropped.resize((size, size), Image.Resampling.LANCZOS)

            # 保存为JPG
            base_name = os.path.splitext(os.path.basename(image_path))[0]
            jpg_path = os.path.join(temp_dir, f"{base_name}.jpg")

            img_resized.save(jpg_path, 'JPEG', quality=quality, optimize=True)

            logger.info(f"Processed cover image: {jpg_path}")
            return jpg_path

    except Exception as e:
        logger.error(f"Failed to process cover image {image_path}: {str(e)}")
        return None

def transcode_file(input_path, format_type, temp_dir, cover_image_path=None, metadata=None, quality=DEFAULT_AUDIO_QUALITY):
    """
    使用ffmpeg转码音频文件，参考前端audioTranscoder逻辑
    """
    try:
        if format_type not in SUPPORTED_FORMATS:
            raise ValueError(f"Unsupported format: {format_type}")

        # 生成输出文件路径
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        output_path = os.path.join(temp_dir, f"{base_name}.{format_type}")

        # 构建ffmpeg命令，参考前端逻辑
        cmd = ['ffmpeg', '-i', input_path]

        # 如果有封面图片，添加图片输入
        if cover_image_path and os.path.exists(cover_image_path):
            cmd.extend(['-i', cover_image_path])
            cmd.extend(['-map', '0:a', '-map', '1'])

        # 根据格式设置编码参数
        if format_type == 'mp3':
            cmd.extend([
                '-codec:a', 'libmp3lame',
                '-b:a', '320k'
            ])
            if cover_image_path:
                cmd.extend([
                    '-c:v', 'mjpeg',
                    '-id3v2_version', '3',
                    '-metadata:s:v', 'title=Album cover',
                    '-metadata:s:v', 'comment=Cover (front)',
                    '-metadata:s:v', 'handler_name=Album cover'
                ])
        elif format_type == 'flac':
            cmd.extend(['-codec:a', 'flac'])
            if cover_image_path:
                cmd.extend([
                    '-metadata:s:v', 'title=Album cover',
                    '-metadata:s:v', 'comment=Cover (front)',
                    '-disposition:v', 'attached_pic'
                ])
        elif format_type == 'wav':
            cmd.extend(['-codec:a', 'pcm_s16le'])
        elif format_type == 'aac':
            cmd.extend([
                '-codec:a', 'aac',
                '-b:a', '320k'
            ])
        elif format_type == 'm4a':
            cmd.extend([
                '-codec:a', 'aac',
                '-b:a', '320k'
            ])
        elif format_type == 'ogg':
            cmd.extend([
                '-codec:a', 'libvorbis',
                '-b:a', '320k'
            ])

        # 添加元数据
        if metadata:
            if metadata.get('title'):
                cmd.extend(['-metadata', f"title={metadata['title']}"])
            if metadata.get('artist'):
                cmd.extend(['-metadata', f"artist={metadata['artist']}"])
            if metadata.get('album'):
                cmd.extend(['-metadata', f"album={metadata['album']}"])

        # 添加自定义元数据
        cmd.extend([
            '-metadata', 'PURL=1music.cc',
            '-metadata', 'COMMENT=1music.cc'
        ])

        # 输出文件和覆盖选项
        cmd.extend(['-y', output_path])

        # 执行转码
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            logger.error(f"FFmpeg error: {result.stderr}")
            raise Exception(f"FFmpeg failed with return code {result.returncode}")

        if os.path.exists(output_path):
            logger.info(f"Transcoded file: {output_path}")
            return output_path
        else:
            raise Exception("Transcoded file not found")

    except Exception as e:
        logger.error(f"Failed to transcode file {input_path} to {format_type}: {str(e)}")
        return None

def upload_to_webdav(file_path, webdav_config, title, artist, album, format_type):
    """
    上传文件到WebDAV服务器
    直接使用配置的URL作为根目录，文件名格式为：歌手-歌曲名.format
    """
    try:
        # 创建WebDAV客户端
        options = {
            'webdav_hostname': webdav_config['url'],
            'webdav_login': webdav_config['username'],
            'webdav_password': webdav_config['password'],
            'webdav_timeout': WEBDAV_TIMEOUT
        }

        # 添加代理配置
        if HTTP_PROXY:
            options['proxy_hostname'] = HTTP_PROXY

        client = Client(options)

        # 清理文件名中的特殊字符
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_', '.')).strip()
        safe_artist = "".join(c for c in artist if c.isalnum() or c in (' ', '-', '_', '.')).strip()

        # 替换多个空格为单个空格
        safe_title = ' '.join(safe_title.split())
        safe_artist = ' '.join(safe_artist.split())

        # 构建文件名：歌手-歌曲名.format
        remote_filename = f"{safe_artist}-{safe_title}.{format_type}"

        # 直接使用根目录，不创建子目录
        remote_path = f"/{remote_filename}"

        logger.info(f"Uploading file to: {remote_path}")

        # 上传文件
        retry_count = 0
        while retry_count < MAX_RETRY_ATTEMPTS:
            try:
                client.upload_sync(remote_path=remote_path, local_path=file_path)
                logger.info(f"Successfully uploaded file to WebDAV: {remote_path}")
                return True

            except Exception as e:
                retry_count += 1
                logger.warning(f"Upload attempt {retry_count} failed: {str(e)}")

                if retry_count >= MAX_RETRY_ATTEMPTS:
                    raise e

        return False

    except Exception as e:
        logger.error(f"Failed to upload file to WebDAV: {str(e)}")
        raise e

def update_task_status(task_id, status, reason=None):
    """
    更新后端任务状态
    """
    try:
        payload = {
            'secret': WEBDAV_SECRET_KEY,
            'task_id': task_id,
            'status': status
        }
        
        if reason:
            payload['reason'] = reason
        
        response = requests.post(
            f"{BACKEND_URL}update_upload_task_status/",
            json=payload,
            timeout=10
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to update task status: {response.status_code}")
            
    except Exception as e:
        logger.error(f"Error updating task status: {str(e)}")

def cleanup_temp_files(*file_paths):
    """
    清理临时文件
    """
    for file_path in file_paths:
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Cleaned up temp file: {file_path}")
        except Exception as e:
            logger.error(f"Failed to cleanup temp file {file_path}: {str(e)}")
